import 'package:flutter/material.dart';
import 'package:octasync_client/api/department.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/models/pages_model/pages_model.dart';

class DepartmentSelector extends StatefulWidget {
  const DepartmentSelector({super.key});

  @override
  State<DepartmentSelector> createState() => _DepartmentSelectorState();
}

class _DepartmentSelectorState extends State<DepartmentSelector> {
  final reqParams = {'PageIndex': 1, 'PageSize': 9999};
  PagesModel<DepartmentModel> _pages = PagesModel();
  List<DepartmentModel> _list = [];

  @override
  void initState() {
    super.initState();
    getList();
  }

  void getList() {
    DepartmentApi.getList(reqParams).then((value) {
      setState(() {
        _pages = PagesModel.fromJson(
          value,
          (json) => DepartmentModel.fromJson(json as Map<String, dynamic>),
        );
        _list = _pages.items;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border(right: BorderSide(color: context.border300, width: 1)),
      ),
      child: Column(children: _list.map((item) => Text(item.departmentName)).toList()),
    );
  }
}
